"use client";

import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, ServerIcon } from "lucide-react";
import Image from "next/image";

interface Server {
  id: string;
  name: string;
  icon: string | null;
}

interface Role {
  id: string;
  name: string;
  color: number;
  position: number;
}

interface DiscordRoleSelectorProps {
  hubId: string;
  value: string;
  onChange: (value: string) => void;
  label: string;
  placeholder: string;
  description?: string;
}

export function DiscordRoleSelector({
  value,
  onChange,
  label,
  placeholder,
  description,
}: DiscordRoleSelectorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [servers, setServers] = useState<Server[]>([]);
  const [selectedServer, setSelectedServer] = useState<string>("");
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedRole, setSelectedRole] = useState<string>(value);

  // Fetch servers that the user owns or moderates
  useEffect(() => {
    const fetchServers = async () => {
      setIsLoading(true);
      try {
        const response = await fetch("/api/discord/user/servers?manageable=true");
        if (!response.ok) {
          throw new Error("Failed to fetch servers");
        }
        const data = await response.json();
        setServers(data.servers || []);
      } catch (error) {
        console.error("Error fetching servers:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchServers();
  }, []);

  // Fetch roles when a server is selected
  useEffect(() => {
    if (!selectedServer) return;

    const fetchRoles = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/discord/servers/${selectedServer}/roles`);
        if (!response.ok) {
          throw new Error("Failed to fetch roles");
        }
        const data = await response.json();
        setRoles(data.roles || []);
      } catch (error) {
        console.error("Error fetching roles:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRoles();
  }, [selectedServer]);

  // Set selected server if we have a role value and servers are loaded
  useEffect(() => {
    if (value && servers.length > 0 && !selectedServer) {
      // If we have a role ID but don't know which server it belongs to,
      // we need to find the server by checking each one's roles
      const checkServerForRole = async (serverId: string) => {
        try {
          const response = await fetch(`/api/discord/servers/${serverId}/roles`);
          if (!response.ok) return false;

          const data = await response.json();
          const roles = data.roles || [];

          return roles.some((role: { id: string }) => role.id === value);
        } catch (error) {
          console.error(`Error checking server ${serverId} for role:`, error);
          return false;
        }
      };

      const findServerWithRole = async () => {
        setIsLoading(true);
        for (const server of servers) {
          const hasRole = await checkServerForRole(server.id);
          if (hasRole) {
            setSelectedServer(server.id);
            break;
          }
        }
        setIsLoading(false);
      };

      findServerWithRole();
    }
  }, [value, servers, selectedServer]);

  // Update the selected role when value changes
  useEffect(() => {
    setSelectedRole(value);
  }, [value]);

  // Handle server selection
  const handleServerChange = (serverId: string) => {
    setSelectedServer(serverId);
    setSelectedRole("");
    onChange("");
  };

  // Handle role selection
  const handleRoleChange = (roleId: string) => {
    setSelectedRole(roleId);
    onChange(roleId);
  };

  // Convert a Discord role color (integer) to a hex color string
  const getRoleColor = (colorInt: number) => {
    if (colorInt === 0) return "#99AAB5"; // Default Discord gray
    return `#${colorInt.toString(16).padStart(6, '0')}`;
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">{label}</Label>
        {description && <span className="text-xs text-gray-400">{description}</span>}
      </div>

      {/* Server Selection */}
      <Select value={selectedServer} onValueChange={handleServerChange}>
        <SelectTrigger className="w-full bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50">
          <SelectValue placeholder="Select a server first" />
        </SelectTrigger>
        <SelectContent className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50 max-h-[300px]">
          {isLoading && !servers.length ? (
            <div className="flex items-center justify-center py-2">
              <Loader2 className="h-4 w-4 animate-spin text-gray-400 mr-2" />
              <span className="text-gray-400">Loading servers...</span>
            </div>
          ) : servers.length === 0 ? (
            <div className="p-2 text-center text-gray-400">
              No servers found where you have manage permissions
            </div>
          ) : (
            servers.map((server) => (
              <SelectItem key={server.id} value={server.id}>
                <div className="flex items-center gap-2">
                  {server.icon ? (
                    <div className="relative h-5 w-5 rounded-full overflow-hidden flex-shrink-0">
                      <Image
                        src={server.icon}
                        alt={server.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <ServerIcon className="h-5 w-5 text-gray-400 flex-shrink-0" />
                  )}
                  <span className="truncate">{server.name}</span>
                </div>
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>

      {/* Role Selection - Only show if a server is selected */}
      {selectedServer && (
        <Select value={selectedRole} onValueChange={handleRoleChange}>
          <SelectTrigger className="w-full bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50">
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50 max-h-[300px]">
            {isLoading ? (
              <div className="flex items-center justify-center py-2">
                <Loader2 className="h-4 w-4 animate-spin text-gray-400 mr-2" />
                <span className="text-gray-400">Loading roles...</span>
              </div>
            ) : roles.length === 0 ? (
              <div className="p-2 text-center text-gray-400">
                No roles found in this server
              </div>
            ) : (
              roles.map((role) => (
                <SelectItem key={role.id} value={role.id}>
                  <div className="flex items-center gap-2">
                    <div
                      className="h-3 w-3 rounded-full flex-shrink-0"
                      style={{ backgroundColor: getRoleColor(role.color) }}
                    />
                    <span className="truncate">{role.name}</span>
                  </div>
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
      )}
    </div>
  );
}
