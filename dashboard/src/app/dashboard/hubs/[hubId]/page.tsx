import { auth } from "@/auth";
import { HubLayout } from "@/components/dashboard/hubs/hub-layout";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import {
  Edit,
  Home,
  MessageSquare,
  Shield,
  Users,
  ExternalLink,
  Star
} from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";

interface HubPageProps {
  params: Promise<{
    hubId: string;
  }>;
}

export async function generateMetadata({
  params,
}: HubPageProps): Promise<Metadata> {
  const { hubId } = await params;
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    select: { name: true },

  });

  return {
    title: hub
      ? `${hub.name} | InterChat Dashboard`
      : "Hub | InterChat Dashboard",
    description: "Manage your InterChat hub",
  };
}

export default async function HubPage({ params }: HubPageProps) {
  const { hubId } = await params;
  const session = await auth();

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/hubs/${hubId}`);
  }

  const permissionLevel = await getUserHubPermission(session.user.id, hubId);

  if (permissionLevel === PermissionLevel.NONE) {
    notFound();
  }

  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    include: {
      connections: {
        orderBy: { lastActive: "desc" },
        select: {
          id: true,
          serverId: true,
          connected: true,
          compact: true,
          createdAt: true,
          lastActive: true,
          server: true,
        }
      },
      moderators: { include: { user: true } },
      upvotes: true,
      owner: true,
    },

  });

  if (!hub) {
    notFound();
  }

  const canEdit = permissionLevel >= PermissionLevel.MANAGER;
  const canModerate = permissionLevel >= PermissionLevel.MODERATOR;

  // Get total infractions count
  const infractionsCount = await prisma.infraction.count({
    where: { hubId },
  });

  // Prepare hub data for the layout
  const hubData = {
    id: hub.id,
    name: hub.name,
    description: hub.description,
    iconUrl: hub.iconUrl,
    bannerUrl: hub.bannerUrl,
    private: hub.private,
    nsfw: hub.nsfw,
    connectionCount: hub.connections.filter(c => c.connected).length,
  };

  // Header actions
  const headerActions = canEdit ? (
    <Button
      asChild
      className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-600/80 hover:to-purple-600/80 border-none"
    >
      <Link href={`/dashboard/hubs/${hubId}/edit`}>
        <Edit className="h-4 w-4 mr-2" />
        Edit Hub
      </Link>
    </Button>
  ) : null;

  return (
    <HubLayout
      hub={hubData}
      currentTab="overview"
      canModerate={canModerate}
      canEdit={permissionLevel >= PermissionLevel.MANAGER}
      headerActions={headerActions}
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Hub Statistics Cards */}
        <div className="lg:col-span-2 space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/20 rounded-lg">
                    <Home className="h-4 w-4 text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Servers</p>
                    <p className="text-xl font-bold text-white">{hub.connections.filter(c => c.connected).length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-500/20 rounded-lg">
                    <Users className="h-4 w-4 text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Members</p>
                    <p className="text-xl font-bold text-white">{hub.moderators.length + 1}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-500/20 rounded-lg">
                    <Shield className="h-4 w-4 text-purple-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Infractions</p>
                    <p className="text-xl font-bold text-white">{infractionsCount}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-amber-500/20 rounded-lg">
                    <Star className="h-4 w-4 text-amber-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Rating</p>
                    <p className="text-xl font-bold text-white">{hub.upvotes.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Hub Details */}
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-indigo-400" />
                Hub Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium text-white mb-2">Description</h3>
                <p className="text-gray-300 leading-relaxed">{hub.description}</p>
              </div>

              {hub.welcomeMessage && (
                <div>
                  <h3 className="font-medium text-white mb-2">Welcome Message</h3>
                  <div className="bg-gray-900/50 rounded-lg p-3 border border-gray-800/50">
                    <p className="text-gray-300 text-sm">{hub.welcomeMessage}</p>
                  </div>
                </div>
              )}

              {hub.rules && hub.rules.length > 0 && (
                <div>
                  <h3 className="font-medium text-white mb-2">Rules</h3>
                  <div className="space-y-2">
                    {hub.rules.slice(0, 3).map((rule, index) => (
                      <div key={index} className="flex items-start gap-2 text-sm">
                        <span className="text-indigo-400 font-medium">{index + 1}.</span>
                        <span className="text-gray-300">{rule}</span>
                      </div>
                    ))}
                    {hub.rules.length > 3 && (
                      <p className="text-xs text-gray-400 mt-2">
                        +{hub.rules.length - 3} more rules
                      </p>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {canEdit && (
                <Button asChild className="w-full bg-indigo-600 hover:bg-indigo-700">
                  <Link href={`/dashboard/hubs/${hubId}/edit`}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Hub
                  </Link>
                </Button>
              )}

              <Button asChild variant="outline" className="w-full border-gray-700 hover:bg-gray-800">
                <Link href={`/hubs/${hubId}`}>
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Public Page
                </Link>
              </Button>

              {canModerate && (
                <>
                  <Button asChild variant="outline" className="w-full border-gray-700 hover:bg-gray-800">
                    <Link href={`/dashboard/hubs/${hubId}/connections`}>
                      <Home className="h-4 w-4 mr-2" />
                      Manage Connections
                    </Link>
                  </Button>

                  <Button asChild variant="outline" className="w-full border-gray-700 hover:bg-gray-800">
                    <Link href={`/dashboard/hubs/${hubId}/members`}>
                      <Users className="h-4 w-4 mr-2" />
                      Manage Members
                    </Link>
                  </Button>
                </>
              )}
            </CardContent>
          </Card>

          {/* Hub Owner */}
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-lg">Hub Owner</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-gradient-to-br from-yellow-500/20 to-orange-500/20 flex items-center justify-center">
                  <Users className="h-5 w-5 text-yellow-400" />
                </div>
                <div>
                  <p className="font-medium text-white">{hub.owner?.name || "Unknown"}</p>
                  <p className="text-sm text-gray-400">Owner</p>
                </div>
                </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </HubLayout>
  );
}
