"use client";

import { useState, useEffect } from "react";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ChannelIcon } from "@/components/discord/channel-icon";
import { Loader2, ServerIcon } from "lucide-react";
import Image from "next/image";

interface Server {
  id: string;
  name: string;
  icon: string | null;
}

interface Channel {
  id: string;
  name: string;
  type: number;
  parentId: string | null;
  parentName: string | null;
  isThread: boolean;
}

interface DiscordChannelSelectorProps {
  hubId: string;
  value: string;
  onChange: (value: string) => void;
  label: string;
  placeholder: string;
  description?: string;
}

export function DiscordChannelSelector({
  value,
  onChange,
  label,
  placeholder,
  description,
}: DiscordChannelSelectorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [servers, setServers] = useState<Server[]>([]);
  const [selectedServer, setSelectedServer] = useState<string>("");
  const [channels, setChannels] = useState<Channel[]>([]);
  const [selectedChannel, setSelectedChannel] = useState<string>(value);

  // Fetch servers that the user owns or moderates
  useEffect(() => {
    const fetchServers = async () => {
      setIsLoading(true);
      try {
        const response = await fetch("/api/discord/user/servers?manageable=true");
        if (!response.ok) {
          throw new Error("Failed to fetch servers");
        }
        const data = await response.json();
        setServers(data.servers || []);
      } catch (error) {
        console.error("Error fetching servers:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchServers();
  }, []);

  // Fetch channels when a server is selected
  useEffect(() => {
    if (!selectedServer) return;

    const fetchChannels = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/discord/servers/${selectedServer}/channels`);
        if (!response.ok) {
          throw new Error("Failed to fetch channels");
        }
        const data = await response.json();
        setChannels(data.channels || []);
      } catch (error) {
        console.error("Error fetching channels:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchChannels();
  }, [selectedServer]);

  // Set selected server if we have a channel value and servers are loaded
  useEffect(() => {
    if (value && servers.length > 0 && !selectedServer) {
      // If we have a channel ID but don't know which server it belongs to,
      // we need to find the server by checking each one's channels
      const checkServerForChannel = async (serverId: string) => {
        try {
          const response = await fetch(`/api/discord/servers/${serverId}/channels`);
          if (!response.ok) return false;

          const data = await response.json();
          const channels = data.channels || [];

          return channels.some((channel: { id: string }) => channel.id === value);
        } catch (error) {
          console.error(`Error checking server ${serverId} for channel:`, error);
          return false;
        }
      };

      const findServerWithChannel = async () => {
        setIsLoading(true);
        for (const server of servers) {
          const hasChannel = await checkServerForChannel(server.id);
          if (hasChannel) {
            setSelectedServer(server.id);
            break;
          }
        }
        setIsLoading(false);
      };

      findServerWithChannel();
    }
  }, [value, servers, selectedServer]);

  // Update the selected channel when value changes
  useEffect(() => {
    setSelectedChannel(value);
  }, [value]);

  // Handle server selection
  const handleServerChange = (serverId: string) => {
    setSelectedServer(serverId);
    setSelectedChannel("");
    onChange("");
  };

  // Handle channel selection
  const handleChannelChange = (channelId: string) => {
    setSelectedChannel(channelId);
    onChange(channelId);
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">{label}</Label>
        {description && <span className="text-xs text-gray-400">{description}</span>}
      </div>

      {/* Server Selection */}
      <Select value={selectedServer} onValueChange={handleServerChange}>
        <SelectTrigger className="w-full bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50">
          <SelectValue placeholder="Select a server first" />
        </SelectTrigger>
        <SelectContent className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50 max-h-[300px]">
          {isLoading && !servers.length ? (
            <div className="flex items-center justify-center py-2">
              <Loader2 className="h-4 w-4 animate-spin text-gray-400 mr-2" />
              <span className="text-gray-400">Loading servers...</span>
            </div>
          ) : servers.length === 0 ? (
            <div className="p-2 text-center text-gray-400">
              No servers found where you have manage permissions
            </div>
          ) : (
            servers.map((server) => (
              <SelectItem key={server.id} value={server.id}>
                <div className="flex items-center gap-2">
                  {server.icon ? (
                    <div className="relative h-5 w-5 rounded-full overflow-hidden flex-shrink-0">
                      <Image
                        src={server.icon}
                        alt={server.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <ServerIcon className="h-5 w-5 text-gray-400 flex-shrink-0" />
                  )}
                  <span className="truncate">{server.name}</span>
                </div>
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>

      {/* Channel Selection - Only show if a server is selected */}
      {selectedServer && (
        <Select value={selectedChannel} onValueChange={handleChannelChange}>
          <SelectTrigger className="w-full bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50">
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50 max-h-[300px]">
            {isLoading ? (
              <div className="flex items-center justify-center py-2">
                <Loader2 className="h-4 w-4 animate-spin text-gray-400 mr-2" />
                <span className="text-gray-400">Loading channels...</span>
              </div>
            ) : channels.length === 0 ? (
              <div className="p-2 text-center text-gray-400">
                No channels found in this server
              </div>
            ) : (
              channels.map((channel) => (
                <SelectItem key={channel.id} value={channel.id}>
                  <div className="flex items-center gap-2">
                    <ChannelIcon type={channel.type} className="h-4 w-4 flex-shrink-0" />
                    <span className="truncate">#{channel.name}</span>
                    {channel.parentName && (
                      <span className="text-xs text-gray-400">
                        (in {channel.parentName})
                      </span>
                    )}
                  </div>
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
      )}
    </div>
  );
}
