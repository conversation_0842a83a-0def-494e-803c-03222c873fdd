import { auth } from "@/auth";
import { HubLayout } from "@/components/dashboard/hubs/hub-layout";
import { Button } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { AlertTriangle, Ban, Shield, Flag } from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";

export default async function HubModerationPage({
  params,
}: {
  params: Promise<{ hubId: string }>;
}) {
  const session = await auth();
  if (!session?.user) {
    redirect("/login");
  }

  const { hubId } = await params;

  // Check if user has permission to view this hub
  const permissionLevel = await getUserHubPermission(session.user.id, hubId);

  // Only allow moderators and managers to access this page
  if (permissionLevel < PermissionLevel.MODERATOR) {
    redirect(`/dashboard/hubs/${hubId}`);
  }

  // Get hub details
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    select: {
      id: true,
      name: true,
      description: true,
      private: true,
      locked: true,
      iconUrl: true,
      bannerUrl: true,
      nsfw: true,
      antiSwearRules: {
        select: {
          id: true,
          name: true,
        },
      },
      infractions: {
        select: {
          id: true,
          status: true,
        },
      },
      moderators: {
        select: {
          id: true,
        },
      },
      blockWords: {
        select: {
          id: true,
        },
      },
      reports: {
        select: {
          id: true,
          status: true,
          createdAt: true,
        },
      },
      _count: {
        select: {
          connections: {
            where: { connected: true }
          }
        }
      }
    },
  });

  if (!hub) {
    redirect("/dashboard/hubs");
  }

  // Hub data is now ready to use

  // Prepare hub data for the layout
  const hubData = {
    id: hub.id,
    name: hub.name,
    description: hub.description,
    iconUrl: hub.iconUrl,
    bannerUrl: hub.bannerUrl,
    private: hub.private,
    nsfw: hub.nsfw,
    connectionCount: hub._count.connections,
  };

  return (
    <HubLayout
      hub={hubData}
      currentTab="moderation"
      canModerate={true}
      canEdit={permissionLevel >= PermissionLevel.MANAGER}
    >
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-500/20 rounded-lg">
                <Ban className="h-4 w-4 text-red-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Active Blacklists</p>
                <p className="text-xl font-bold text-white">
                  {hub.infractions.filter((i) => i.status === "ACTIVE").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-amber-500/20 rounded-lg">
                <AlertTriangle className="h-4 w-4 text-amber-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Total Infractions</p>
                <p className="text-xl font-bold text-white">{hub.infractions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-500/20 rounded-lg">
                <Flag className="h-4 w-4 text-red-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Reports</p>
                <p className="text-xl font-bold text-white">{hub.reports.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <Shield className="h-4 w-4 text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Moderators</p>
                <p className="text-xl font-bold text-white">{hub.moderators.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Shield className="h-4 w-4 text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Block Words</p>
                <p className="text-xl font-bold text-white">{hub.blockWords.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Moderation Tools */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {/* Blacklist Management */}
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Ban className="h-5 w-5 text-red-400" />
              Blacklist Management
            </CardTitle>
            <CardDescription>
              Manage blacklisted users and servers
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-800/50">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-300">Active Blacklists</span>
                <span className="text-lg font-bold text-red-400">
                  {hub.infractions.filter((i) => i.status === "ACTIVE").length}
                </span>
              </div>
              <p className="text-xs text-gray-400">
                Users and servers currently blocked from this hub
              </p>
            </div>

            <div className="space-y-2">
              <Button
                asChild
                className="w-full bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-600/80 hover:to-pink-600/80 border-none"
              >
                <Link href={`/dashboard/moderation/blacklist/add?hubId=${hubId}`}>
                  <Ban className="h-4 w-4 mr-2" />
                  Add Blacklist Entry
                </Link>
              </Button>

              <Button
                asChild
                variant="outline"
                className="w-full border-gray-700 hover:bg-gray-800 hover:text-white"
              >
                <Link href={`/dashboard/moderation/blacklist?hubId=${hubId}`}>
                  <Shield className="h-4 w-4 mr-2" />
                  Manage Blacklist
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Infractions */}
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-400" />
              Infractions
            </CardTitle>
            <CardDescription>
              View and manage user infractions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-800/50">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-300">Total Infractions</span>
                <span className="text-lg font-bold text-amber-400">
                  {hub.infractions.length}
                </span>
              </div>
              <p className="text-xs text-gray-400">
                History of all infractions issued in this hub
              </p>
            </div>

            <Button
              asChild
              className="w-full bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-600/80 hover:to-orange-600/80 border-none"
            >
              <Link href={`/dashboard/hubs/${hubId}/infractions`}>
                <AlertTriangle className="h-4 w-4 mr-2" />
                View All Infractions
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Reports */}
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Flag className="h-5 w-5 text-red-400" />
              Reports Management
            </CardTitle>
            <CardDescription>
              Review and manage user reports
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-800/50">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-300">Total Reports</span>
                <span className="text-lg font-bold text-red-400">
                  {hub.reports.length}
                </span>
              </div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-gray-400">Pending Review</span>
                <span className="text-sm font-medium text-amber-400">
                  {hub.reports.filter(r => r.status === 'PENDING').length}
                </span>
              </div>
            </div>

            <Button
              asChild
              className="w-full bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-600/80 hover:to-pink-600/80 border-none"
            >
              <Link href={`/dashboard/moderation/reports?hubId=${hubId}`}>
                <Flag className="h-4 w-4 mr-2" />
                Manage Reports
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Anti-Swear */}
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-purple-400" />
              Anti-Swear
            </CardTitle>
            <CardDescription>
              Configure anti-swear settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-800/50">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-300">Block Words</span>
                <span className="text-lg font-bold text-purple-400">
                  {hub.blockWords.length}
                </span>
              </div>
              <p className="text-xs text-gray-400">
                Words and phrases blocked in this hub
              </p>
            </div>

            <Button
              asChild
              className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-600/80 hover:to-indigo-600/80 border-none"
            >
              <Link href={`/dashboard/hubs/${hubId}/anti-swear`}>
                <Shield className="h-4 w-4 mr-2" />
                Configure Anti-Swear
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </HubLayout>
  );
}
