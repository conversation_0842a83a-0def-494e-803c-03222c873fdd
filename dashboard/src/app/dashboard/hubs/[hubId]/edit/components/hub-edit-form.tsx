"use client";

import { HubTagManagement } from '@/components/dashboard/hubs/HubTagManagement';
import { HubBannerManagement } from '@/components/dashboard/hubs/HubBannerManagement';
import { HubIconManagement } from '@/components/dashboard/hubs/HubIconManagement';
import { HubLanguageManagement } from '@/components/dashboard/hubs/HubLanguageManagement';
import { HubNSFWToggle } from '@/components/dashboard/hubs/HubNSFWToggle';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import {
  ArrowUp,
  ArrowDown,
  Loader2,
  Save,
  Trash2,
  <PERSON>lette,
  Settings,
  Edit3,
  Plus,
  X,
  Globe,
  Shield,
  MessageSquare,
  Image as ImageIcon
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface HubData {
  id: string;
  name: string;
  description: string;
  private: boolean;
  welcomeMessage: string | null;
  rules: string[];
  bannerUrl: string | null;
  iconUrl: string | null;
  language: string | null;
  nsfw: boolean;
  tags: string[];
  connectionCount: number;
  isOwner: boolean;
  canEdit: boolean;
}

interface HubEditFormProps {
  hubData: HubData;
}

export function HubEditForm({ hubData }: HubEditFormProps) {
  const [name, setName] = useState(hubData.name);
  const [description, setDescription] = useState(hubData.description);
  const [isPrivate, setIsPrivate] = useState(hubData.private);
  const [welcomeMessage, setWelcomeMessage] = useState(hubData.welcomeMessage || '');
  const [rules, setRules] = useState<string[]>(hubData.rules || []);
  const [newRule, setNewRule] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Additional state for customize functionality
  const [bannerUrl, setBannerUrl] = useState<string | undefined>(hubData.bannerUrl || undefined);
  const [iconUrl, setIconUrl] = useState<string | undefined>(hubData.iconUrl || undefined);
  const [currentTags, setCurrentTags] = useState<string[]>(hubData.tags);
  const [language, setLanguage] = useState<string | undefined>(hubData.language || undefined);
  const [nsfw, setNsfw] = useState(hubData.nsfw);

  const { toast } = useToast();
  const router = useRouter();

  const supportedWelcomeVariables = [
    '{user}',
    '{hubName}',
    '{serverName}',
    '{memberCount}',
    '{totalConnections}',
  ];

  const formattedWelcomeVariablesList = supportedWelcomeVariables.map((variable) => (
    <code
      key={variable}
      className="bg-indigo-500/20 px-2 py-1 rounded cursor-pointer inline-block hover:bg-indigo-500/30 transition-colors text-indigo-300"
      onClick={() => setWelcomeMessage(welcomeMessage + variable)}
    >
      {variable}
    </code>
  ));

  const handleAddRule = () => {
    if (newRule.trim() && !rules.includes(newRule.trim())) {
      setRules([...rules, newRule.trim()]);
      setNewRule('');
    }
  };

  const handleRemoveRule = (index: number) => {
    setRules(rules.filter((_, i) => i !== index));
  };

  const handleMoveRuleUp = (index: number) => {
    if (index === 0) return;
    const newRules = [...rules];
    [newRules[index - 1], newRules[index]] = [newRules[index], newRules[index - 1]];
    setRules(newRules);
  };

  const handleMoveRuleDown = (index: number) => {
    if (index === rules.length - 1) return;
    const newRules = [...rules];
    [newRules[index], newRules[index + 1]] = [newRules[index + 1], newRules[index]];
    setRules(newRules);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/hubs/${hubData.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          description,
          private: isPrivate,
          welcomeMessage: welcomeMessage || null,
          rules,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update hub');
      }

      toast({
        title: '🎉 Hub Updated Successfully!',
        description: 'Your hub settings have been saved.',
      });

      // Refresh the page to see the updated data
      router.refresh();
    } catch (error) {
      console.error('Error updating hub:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update hub',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Basic Information Section */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm shadow-xl">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center">
                <Edit3 className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl">Basic Information</CardTitle>
                <CardDescription className="text-base">
                  Update your hub&apos;s core settings and content
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="name" className="text-base font-medium">Hub Name</Label>
                <Input
                  id="name"
                  placeholder="Enter hub name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  minLength={3}
                  maxLength={32}
                  className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50"
                />
                <p className="text-xs text-gray-400">Choose a unique name between 3-32 characters.</p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Switch
                    id="private"
                    checked={isPrivate}
                    onCheckedChange={setIsPrivate}
                    className="data-[state=checked]:bg-indigo-600"
                  />
                  <Label htmlFor="private" className="text-base font-medium">Private Hub</Label>
                </div>
                <p className="text-xs text-gray-400">
                  Private hubs are only visible to members and require an invitation to join.
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <Label htmlFor="description" className="text-base font-medium">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your hub"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                required
                minLength={10}
                maxLength={500}
                className="min-h-[120px] bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 resize-none"
              />
              <div className="flex justify-between">
                <p className="text-xs text-gray-400">
                  Provide a clear description between 10-500 characters.
                </p>
                <span className="text-xs text-gray-400">{description.length}/500</span>
              </div>
            </div>

            <div className="space-y-3">
              <Label htmlFor="welcomeMessage" className="text-base font-medium">
                Welcome Message <span className="text-gray-400">(Optional)</span>
              </Label>
              <Textarea
                id="welcomeMessage"
                placeholder="Welcome message for new members"
                value={welcomeMessage}
                onChange={(e) => setWelcomeMessage(e.target.value)}
                maxLength={1000}
                className="min-h-[120px] bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 resize-none"
              />
              <div className="space-y-2">
                <div className="flex justify-between">
                  <p className="text-xs text-gray-400">
                    This message will be shown to <b>all servers</b> when a new server joins your hub.
                  </p>
                  <span className="text-xs text-gray-400">{welcomeMessage.length}/1000</span>
                </div>
                <div className="text-xs text-gray-400">
                  <p className="mb-2">You can use these variables:</p>
                  <div className="flex flex-wrap gap-2">
                    {formattedWelcomeVariablesList}
                  </div>
                </div>
              </div>
            </div>

            {/* Rules Section */}
            <div className="space-y-4 pt-6 border-t border-gray-700/50">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">Hub Rules</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddRule}
                  disabled={!newRule.trim()}
                  className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Rule
                </Button>
              </div>

              <div className="flex gap-3">
                <Textarea
                  placeholder="Add a new rule (e.g., Be respectful to all members)"
                  value={newRule}
                  onChange={(e) => setNewRule(e.target.value)}
                  className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 min-h-[80px] resize-none flex-1"
                  maxLength={200}
                />
              </div>

              <div className="space-y-3">
                {rules.length > 0 ? (
                  rules.map((rule, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-3 p-4 rounded-lg bg-gray-800/30 border border-gray-700/50"
                    >
                      <div className="flex flex-col gap-1">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-gray-400 hover:text-white hover:bg-gray-700/50"
                                onClick={() => handleMoveRuleUp(index)}
                                disabled={index === 0}
                              >
                                <ArrowUp className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Move rule up</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-gray-400 hover:text-white hover:bg-gray-700/50"
                                onClick={() => handleMoveRuleDown(index)}
                                disabled={index === rules.length - 1}
                              >
                                <ArrowDown className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Move rule down</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="flex-1">
                        <div className="text-xs text-indigo-400 font-medium mb-1">Rule {index + 1}</div>
                        <span className="text-sm text-gray-200">{rule}</span>
                      </div>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveRule(index)}
                              className="h-8 w-8 text-red-400 hover:text-red-300 hover:bg-red-900/30"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Delete this rule</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-400">
                    <MessageSquare className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p className="text-sm">No rules added yet.</p>
                    <p className="text-xs">Add rules to help maintain a positive community environment.</p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none py-3"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving Changes...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>

      {/* Discoverability Settings Section */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm shadow-xl">
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
              <Globe className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-xl">Discoverability Settings</CardTitle>
              <CardDescription className="text-base">
                Manage tags, language, and content settings to help users find your hub
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-8">
          <HubTagManagement
            hubId={hubData.id}
            currentTags={currentTags}
            onTagsUpdate={(tags) => setCurrentTags(tags)}
          />
          <div className="border-t border-gray-700/50 pt-8">
            <HubLanguageManagement hubId={hubData.id} currentLanguage={language} />
          </div>
          <div className="border-t border-gray-700/50 pt-8">
            <HubNSFWToggle hubId={hubData.id} currentNsfw={nsfw} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
