import { auth } from "@/auth";
import { DeleteHubDialog } from '@/components/dashboard/hubs/delete-hub-dialog';
import { HubLayout } from '@/components/dashboard/hubs/hub-layout';
import { Button } from '@/components/ui/button';
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import {
    Edit3,
    Globe,
    Palette
} from 'lucide-react';
import type { Metadata } from "next";
import Link from 'next/link';
import { notFound, redirect } from "next/navigation";
import { HubEditForm } from "./components/hub-edit-form";

interface HubEditPageProps {
  params: Promise<{
    hubId: string;
  }>;
}

export async function generateMetadata({
  params,
}: HubEditPageProps): Promise<Metadata> {
  const { hubId } = await params;
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    select: { name: true },
  });

  return {
    title: hub ? `Edit ${hub.name} | InterChat Dashboard` : "Edit Hub | InterChat Dashboard",
    description: "Manage your InterChat hub settings, appearance, and configuration",
  };
}

export default async function HubEditPage({
  params,
}: HubEditPageProps) {
  const { hubId } = await params;
  const session = await auth();

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/hubs/${hubId}/edit`);
  }

  const permissionLevel = await getUserHubPermission(session.user.id, hubId);
  const canEdit = permissionLevel >= PermissionLevel.MANAGER;
  const isOwner = permissionLevel === PermissionLevel.OWNER;

  if (permissionLevel === PermissionLevel.NONE) {
    notFound();
  }

  if (!canEdit) {
    redirect(`/dashboard/hubs/${hubId}`);
  }

  // Fetch hub data with all necessary relations
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    include: {
      tags: {
        select: { name: true }
      },
      _count: {
        select: {
          connections: {
            where: { connected: true }
          },
          upvotes: true
        }
      }
    },
  });

  if (!hub) {
    notFound();
  }

  // Transform data for client component
  const hubData = {
    id: hub.id,
    name: hub.name,
    description: hub.description,
    private: hub.private,
    welcomeMessage: hub.welcomeMessage,
    rules: hub.rules,
    bannerUrl: hub.bannerUrl,
    iconUrl: hub.iconUrl,
    language: hub.language,
    nsfw: hub.nsfw,
    tags: hub.tags.map(tag => tag.name),
    connectionCount: hub._count.connections,
    isOwner,
    canEdit,
  };

  // Prepare hub data for the layout (only the required fields)
  const hubLayoutData = {
    id: hub.id,
    name: hub.name,
    description: hub.description,
    iconUrl: hub.iconUrl,
    bannerUrl: hub.bannerUrl,
    private: hub.private,
    nsfw: hub.nsfw,
    connectionCount: hub._count.connections,
  };

  return (
    <HubLayout
      hub={hubLayoutData}
      currentTab="edit"
      canModerate={permissionLevel >= PermissionLevel.MODERATOR}
      canEdit={canEdit}
      headerActions={
        <div className="flex items-center gap-3">
          <Button
            asChild
            variant="outline"
            className="border-gray-700 hover:bg-gray-800 hover:text-white"
          >
            <Link href={`/hubs/${hubId}`}>
              <Globe className="h-4 w-4 mr-2" />
              View Public
            </Link>
          </Button>

          {isOwner && (
            <DeleteHubDialog hubId={hubId} hubName={hubData.name} />
          )}
        </div>
      }
    >
      <div className="space-y-8">
        {/* Main Content */}
        <div className="max-w-6xl mx-auto">
            {/* Feature Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-gradient-to-br from-purple-500/10 to-indigo-600/10 border border-purple-500/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <Edit3 className="h-5 w-5 text-purple-400" />
                  </div>
                  <h3 className="font-semibold text-white">Basic Settings</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Update your hub&apos;s name, description, privacy settings, and community rules
                </p>
              </div>

              <div className="bg-gradient-to-br from-blue-500/10 to-cyan-600/10 border border-blue-500/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <Palette className="h-5 w-5 text-blue-400" />
                  </div>
                  <h3 className="font-semibold text-white">Appearance</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Customize your hub&apos;s visual identity with banners, icons, and branding
                </p>
              </div>

              <div className="bg-gradient-to-br from-green-500/10 to-emerald-600/10 border border-green-500/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <Globe className="h-5 w-5 text-green-400" />
                  </div>
                  <h3 className="font-semibold text-white">Discovery</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Manage tags, language settings, and content preferences for better discoverability
                </p>
              </div>
            </div>

          {/* Edit Form */}
          <HubEditForm hubData={hubData} />
        </div>
      </div>
    </HubLayout>
  );
}
