import { auth } from "@/auth";
import { HubLayout } from "@/components/dashboard/hubs/hub-layout";
import { HubLoggingForm } from "@/components/dashboard/hubs/hub-logging-form";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { AlertCircle, FileText, MessageSquare, Shield } from "lucide-react";
import { notFound, redirect } from "next/navigation";

interface HubLoggingPageProps {
  params: Promise<{
    hubId: string;
  }>;
}

export default async function HubLoggingPage({
  params: paramsPromise,
}: HubLoggingPageProps) {
  const { hubId } = await paramsPromise;
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Get the user's permission level for this hub
  const permissionLevel = await getUserHubPermission(session.user.id, hubId);
  const canModerate = permissionLevel >= PermissionLevel.MODERATOR;
  const canEdit = permissionLevel >= PermissionLevel.MANAGER;

  // If the user doesn't have permission to edit the hub, redirect to the hub overview
  if (!canEdit) {
    redirect(`/dashboard/hubs/${hubId}`);
  }

  // Get the hub data
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    include: {
      logConfig: true,
      _count: {
        select: {
          connections: {
            where: { connected: true }
          }
        }
      }
    },
  });

  if (!hub) {
    notFound();
  }

  // Prepare hub data for the layout
  const hubData = {
    id: hub.id,
    name: hub.name,
    description: hub.description,
    iconUrl: hub.iconUrl,
    bannerUrl: hub.bannerUrl,
    private: hub.private,
    nsfw: hub.nsfw,
    connectionCount: hub._count.connections,
  };

  // Get logging stats
  const logConfig = hub.logConfig;
  const configuredLogs = logConfig ? [
    logConfig.modLogsChannelId ? 'modLogs' : null,
    logConfig.joinLeavesChannelId ? 'joinLeaves' : null,
    logConfig.appealsChannelId ? 'appeals' : null,
    logConfig.reportsChannelId ? 'reports' : null,
    logConfig.networkAlertsChannelId ? 'networkAlerts' : null,
    logConfig.messageModerationChannelId ? 'messageModeration' : null,
  ].filter(Boolean) : [];

  return (
    <HubLayout
      hub={hubData}
      currentTab="logging"
      canModerate={canModerate}
      canEdit={canEdit}
    >
      <div className="space-y-6">
        {/* Logging Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-500/20 rounded-lg">
                  <FileText className="h-4 w-4 text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Configured Logs</p>
                  <p className="text-xl font-bold text-white">{configuredLogs.length}/6</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <Shield className="h-4 w-4 text-blue-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Moderation Logs</p>
                  <p className="text-lg font-bold text-white">
                    {logConfig?.modLogsChannelId ? 'Active' : 'Inactive'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <MessageSquare className="h-4 w-4 text-purple-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Message Logs</p>
                  <p className="text-lg font-bold text-white">
                    {logConfig?.messageModerationChannelId ? 'Active' : 'Inactive'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Configuration */}
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-indigo-400" />
              Logging Configuration
            </CardTitle>
            <CardDescription>
              Configure logging channels for different hub activities. Each log type can have its own channel and optional role mentions.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {canEdit ? (
              <HubLoggingForm
                hubId={hubId}
                initialLogConfig={hub.logConfig || null}
              />
            ) : (
              <div className="flex items-center justify-center p-6">
                <AlertCircle className="h-5 w-5 mr-2 text-amber-500" />
                <p className="text-gray-400">
                  You don&apos;t have permission to edit this hub.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </HubLayout>
  );
}
